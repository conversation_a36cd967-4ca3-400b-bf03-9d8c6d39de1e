#!/usr/bin/env python3
"""
富途牛牛API监控脚本
每分钟请求一次接口并打印返回结果
"""

import requests
import time
import json
from datetime import datetime
import sys
import os
from config import STOCK_CONFIG, AUTH_CONFIG, REQUEST_CONFIG, OUTPUT_CONFIG, COMMON_STOCKS


class FutunnAPIMonitor:
    def __init__(self, stock_id=None):
        self.stock_id = stock_id or STOCK_CONFIG["default_stock_id"]
        self.base_url = "https://www.futunn.com/quote-api/quote-v2/get-real-cash-trend"
        self.retry_count = 0

        # 基础headers，包含必要的认证信息
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'futu-x-csrf-token': AUTH_CONFIG["csrf_token"],
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'quote-token': AUTH_CONFIG["quote_token"],
            'referer': 'https://www.futunn.com/stock/TSLA-US',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        # Cookies - 这些可能需要定期更新
        self.cookies = {
            'csrfToken': AUTH_CONFIG["csrf_token"],
            'locale': 'zh-cn',
            'cipher_device_id': AUTH_CONFIG["device_id"],
            'device_id': AUTH_CONFIG["device_id"],
            'futu-csrf': '5K67RyVx6FyDUNl9c0DKZYkYyrA=',
            'Hm_lvt_f3ecfeb354419b501942b6f9caf8d0db': '**********',
            'HMACCOUNT': 'A760FFA2A234F799',
            '_gid': 'GA1.2.**********.**********',
            '_gcl_au': '1.1.**********.**********',
            '_ga_NTZDYESDX1': 'GS2.2.s1748620857$o1$g0$t1748620857$j60$l0$h0',
            '_uetsid': '46f116303d6f11f08933cddc685b988d',
            '_uetvid': '46f14e603d6f11f0a5bd63862f5b75fe',
            '_clck': 'o82gco%7C2%7Cfwc%7C0%7C1976',
            '_clsk': 'ofgjjj%7C1748620861524%7C1%7C1%7Cz.clarity.ms%2Fcollect',
            'uid': AUTH_CONFIG["uid"],
            'web_sig': 'oy%2BL5KmqMz1cw09FTDcLPgJJjIjXlJW7axvT6JEkD9cD%2Bh0ZulMOC6L9dS0fGHwVLUW4CNV592jmZ8t02H2DJyMD8E8fpmkREGIwVStuHYy8ISp4di9Fz48Yp6O3Qur9Kcrf5iNUUaOzOTKsUlGXxQ%3D%3D',
            'ci_sig': 'tTtCNBBWJHAWYHdOupwfxmJAq6RhPTEwMDAwNTM4JmI9MjAxMTM2Jms9QUtJRFZvdWxPbGNoS2lxUmxxbFlKcDhCMWFDYW1qYmM5U2JxJmU9MTc1MTIxMjg3OCZ0PTE3NDg2MjA4Nzgmcj00ODYzNDU2NzgmdT0mZj0%3D',
            '_ga_K1RSSMGBHL': 'GS2.1.s1748620857$o1$g1$t1748620874$j43$l0$h0',
            'showWatch': '1',
            '_ga_25WYRC4KDG': 'GS2.1.s1748620848$o1$g1$t1748621579$j46$l0$h0',
            'Hm_lpvt_f3ecfeb354419b501942b6f9caf8d0db': '1748621580',
            'ftreport-jssdk%40session': '{"distinctId":"ftv1bJyVhg4E/KYj2N2slRG9SesPFY5CcOtBb0n/FK5of5GnwDp/hKPqOYZmuRWA8Tee","firstId":"ftv1FlTicRb/1GhH05FzPtBKU0wmdEBJ22QL2zDNTSGC1KeufgWsv9nKMeap4Kz1CsoN","latestReferrer":"https://www.futunn.com/quote/us/stock-list/all-us-stocks/top-turnover?chain_id=2iApsYqBLa402t.1k3jliu&global_content={%5C%22promote_id%5C%22%253A13766%2C%5C%22sub_promote_id%5C%22%253A2%2C%5C%22f%5C%22%253A%5C%22nn%252Fquote%252Fus%252Fstock-list%252Fall-us-stocks%252Ftop-turnover%5C%22}"}',
            '_ga': 'GA1.1.121647943.**********',
            '_ga_XECT8CPR37': 'GS2.1.s**********$o1$g1$t1748621580$j45$l0$h0',
            '_ga_370Q8HQYD7': 'GS2.2.s1748620669$o1$g1$t1748621580$j60$l0$h0',
            'locale.sig': 'ObiqV0BmZw7fEycdGJRoK-Q0Yeuop294gBeiHL1LqgQ',
            '_ga_EJJJZFNPTW': 'GS2.1.s**********$o1$g1$t1748621590$j35$l0$h0'
        }

    def generate_timestamp(self):
        """生成时间戳参数（毫秒级）"""
        return int(time.time() * 1000)

    def make_request(self):
        """发起API请求"""
        # 动态参数
        params = {
            'stockId': self.stock_id,
            '_': self.generate_timestamp()  # 时间戳参数，防止缓存
        }
        
        try:
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                cookies=self.cookies,
                timeout=REQUEST_CONFIG["timeout"]
            )
            
            return response
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def format_response(self, response):
        """格式化响应数据"""
        if response is None:
            return "请求失败，无响应数据"
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        result = f"\n{'='*60}\n"
        result += f"请求时间: {timestamp}\n"
        result += f"状态码: {response.status_code}\n"
        result += f"股票ID: {self.stock_id}\n"
        result += f"请求URL: {response.url}\n"
        result += f"{'='*60}\n"
        
        if response.status_code == 200:
            try:
                data = response.json()
                result += f"响应数据:\n{json.dumps(data, indent=2, ensure_ascii=False)}\n"
            except json.JSONDecodeError:
                result += f"响应内容 (非JSON):\n{response.text}\n"
        else:
            result += f"错误响应:\n{response.text}\n"
        
        result += f"{'='*60}\n"
        return result

    def run_monitor(self, interval_minutes=1):
        """运行监控，每隔指定分钟数请求一次"""
        print(f"开始监控富途牛牛API (股票ID: {self.stock_id})")
        print(f"请求间隔: {interval_minutes} 分钟")
        print("按 Ctrl+C 停止监控\n")
        
        try:
            while True:
                response = self.make_request()
                formatted_result = self.format_response(response)
                print(formatted_result)
                
                # 等待指定时间
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
            sys.exit(0)


def main():
    """主函数"""
    # 可以修改这些参数
    stock_id = "201335"  # 默认股票ID，可以修改为其他股票
    interval_minutes = 1  # 请求间隔（分钟）
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        stock_id = sys.argv[1]
    if len(sys.argv) > 2:
        try:
            interval_minutes = float(sys.argv[2])
        except ValueError:
            print("警告: 间隔时间参数无效，使用默认值1分钟")
    
    monitor = FutunnAPIMonitor(stock_id)
    monitor.run_monitor(interval_minutes)


if __name__ == "__main__":
    main()
