#!/usr/bin/env python3
"""
JSON数据监控脚本
每分钟读取本地JSON文件并解析data.list数据
"""

import time
import json
from datetime import datetime
import sys
import os


# 股票配置
STOCK_CONFIG = {
    "201335": "特斯拉",  # 特斯拉
    "202597": "英伟达"   # 英伟达
}


class JSONDataMonitor:
    def __init__(self, stock_id=None):
        self.stock_id = stock_id or "201335"  # 默认特斯拉
        self.downloads_path = os.path.expanduser("~/Downloads")
        self.last_super_net_in = None  # 记录上次的superNetIn值
        self.last_analysis_result = None  # 记录上次的分析结果

        # 验证股票ID
        if self.stock_id not in STOCK_CONFIG:
            print(f"警告: 未知的股票ID {self.stock_id}，支持的股票ID: {list(STOCK_CONFIG.keys())}")

        print(f"监控股票: {STOCK_CONFIG.get(self.stock_id, '未知')} (ID: {self.stock_id})")

    def clear_console(self):
        """清除控制台"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def read_json_file(self):
        """读取JSON文件"""
        json_file_path = os.path.join(self.downloads_path, f"{self.stock_id}.json")

        try:
            if not os.path.exists(json_file_path):
                print(f"错误: JSON文件不存在: {json_file_path}")
                return None

            with open(json_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                return data

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None

    def analyze_super_net_in_changes(self, data_list):
        """分析超大单资金净流入变化"""
        if not data_list or len(data_list) == 0:
            return "没有数据可分析"

        stock_name = STOCK_CONFIG.get(self.stock_id, '未知')

        # 获取最新数据
        latest_data = data_list[-1]
        latest_super_net_in = int(latest_data.get('superNetIn', 0))

        # 先除以1000转换为元，再除以10000转换为万元
        latest_super_net_in_wan = latest_super_net_in / 1000 / 10000

        result = f"\n{stock_name}超大单资金净流入分析:\n"
        result += f"{stock_name}当前超大单资金净流入为{latest_super_net_in_wan:.2f}万元\n"
        result += "-" * 50 + "\n"

        # 分析不同时间段的变化
        time_periods = [1, 3, 5, 10]

        for period in time_periods:
            if len(data_list) > period:
                # 获取前N分钟的数据
                previous_data = data_list[-(period + 1)]
                previous_super_net_in = int(previous_data.get('superNetIn', 0))
                previous_super_net_in_wan = previous_super_net_in / 1000 / 10000

                # 计算变化
                change = latest_super_net_in_wan - previous_super_net_in_wan

                if change >= 0:
                    result += f"比过去{period}分钟增加了{change:.2f}万元\n"
                else:
                    result += f"比过去{period}分钟减少了{abs(change):.2f}万元\n"
            else:
                result += f"比过去{period}分钟: 数据不足（仅有{len(data_list)}条记录）\n"

        return result

    def extract_and_format_data(self, json_data):
        """提取并格式化data.list数据"""
        if json_data is None:
            return None, "读取失败，无数据"

        try:
            # 检查JSON结构
            if 'code' not in json_data:
                return None, "错误: JSON格式不正确，缺少'code'字段"

            if json_data['code'] != 0:
                return None, f"API返回错误: {json_data.get('message', '未知错误')}"

            # 提取data.list
            data = json_data.get('data', {})
            data_list = data.get('list', [])

            if not data_list:
                return None, "data.list为空，没有数据"

            # 获取最新的superNetIn值
            latest_data = data_list[-1]
            current_super_net_in = int(latest_data.get('superNetIn', 0))

            # 检查值是否有变化
            if self.last_super_net_in is not None and current_super_net_in == self.last_super_net_in:
                return None, None  # 值没有变化，不输出

            # 更新记录的值
            self.last_super_net_in = current_super_net_in

            # 生成分析结果
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = f"\n{'='*60}\n"
            result += f"读取时间: {timestamp}\n"
            result += f"股票: {STOCK_CONFIG.get(self.stock_id, '未知')} (ID: {self.stock_id})\n"
            result += f"{'='*60}\n"

            # 只显示超大单资金净流入分析
            result += self.analyze_super_net_in_changes(data_list)
            result += f"{'='*60}\n"

            return current_super_net_in, result

        except Exception as e:
            return None, f"数据解析错误: {e}"

    def run_monitor(self, interval_minutes=1):
        """运行监控，每隔指定分钟数读取JSON文件"""
        print(f"开始监控JSON数据 (股票: {STOCK_CONFIG.get(self.stock_id, '未知')})")
        print(f"读取间隔: {interval_minutes} 分钟")
        print(f"JSON文件路径: {os.path.join(self.downloads_path, f'{self.stock_id}.json')}")
        print("按 Ctrl+C 停止监控")
        print("注意: 只有当超大单资金净流入值发生变化时才会显示新的分析结果\n")

        try:
            while True:
                json_data = self.read_json_file()
                current_value, formatted_result = self.extract_and_format_data(json_data)

                # 只有当有结果且值发生变化时才输出
                if formatted_result is not None and formatted_result != "":
                    # 清除控制台
                    self.clear_console()

                    # 重新显示标题信息
                    print(f"监控中... (股票: {STOCK_CONFIG.get(self.stock_id, '未知')})")
                    print(f"读取间隔: {interval_minutes} 分钟")
                    print("按 Ctrl+C 停止监控")

                    # 显示分析结果
                    print(formatted_result)
                elif formatted_result is not None and current_value is None:
                    # 如果有错误信息，显示错误
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 错误: {formatted_result}")

                # 等待指定时间
                time.sleep(interval_minutes * 60)

        except KeyboardInterrupt:
            print("\n监控已停止")
            sys.exit(0)


def main():
    """主函数"""
    # 默认参数
    stock_id = "201335"  # 默认特斯拉
    interval_minutes = 1  # 默认1分钟

    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        # 检查是否是支持的股票ID
        if sys.argv[1] in STOCK_CONFIG:
            stock_id = sys.argv[1]
            print(f"使用股票: {STOCK_CONFIG[stock_id]} (ID: {stock_id})")
        else:
            print(f"警告: 不支持的股票ID {sys.argv[1]}，支持的股票ID: {list(STOCK_CONFIG.keys())}")
            print(f"使用默认股票: {STOCK_CONFIG[stock_id]} (ID: {stock_id})")

    if len(sys.argv) > 2:
        try:
            interval_minutes = float(sys.argv[2])
        except ValueError:
            print(f"警告: 间隔时间参数无效，使用默认值{interval_minutes}分钟")

    print(f"配置信息:")
    print(f"- 股票: {STOCK_CONFIG.get(stock_id, '未知')} (ID: {stock_id})")
    print(f"- 读取间隔: {interval_minutes} 分钟")
    print()

    monitor = JSONDataMonitor(stock_id)
    monitor.run_monitor(interval_minutes)


if __name__ == "__main__":
    main()
